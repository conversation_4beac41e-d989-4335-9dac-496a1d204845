import SwiftUI

struct AuthenticationView: View {
    @State private var showingLogin = true
    
    var body: some View {
        ZStack {
            // Background with cosmic grid pattern
            Color.background
                .ignoresSafeArea()
            
            // Cosmic grid pattern
            CosmicGridPattern()
                .opacity(0.3)
                .ignoresSafeArea()
            
            VStack {
                if showingLogin {
                    LoginView(showingLogin: $showingLogin)
                } else {
                    RegisterView(showingLogin: $showingLogin)
                }
            }
        }
        .environment(\.layoutDirection, .rightToLeft) // RTL support
    }
}

struct CosmicGridPattern: View {
    var body: some View {
        Canvas { context, size in
            let spacing: CGFloat = 30
            
            for x in stride(from: 0, through: size.width, by: spacing) {
                context.stroke(
                    Path { path in
                        path.move(to: CGPoint(x: x, y: 0))
                        path.addLine(to: CGPoint(x: x, y: size.height))
                    },
                    with: .color(Color.white.opacity(0.05)),
                    lineWidth: 1
                )
            }
            
            for y in stride(from: 0, through: size.height, by: spacing) {
                context.stroke(
                    Path { path in
                        path.move(to: CGPoint(x: 0, y: y))
                        path.addLine(to: CGPoint(x: size.width, y: y))
                    },
                    with: .color(Color.white.opacity(0.05)),
                    lineWidth: 1
                )
            }
        }
    }
}

struct LoginView: View {
    @EnvironmentObject var authViewModel: AuthViewModel
    @Binding var showingLogin: Bool
    
    @State private var email = ""
    @State private var password = ""
    @State private var showingPassword = false
    
    var body: some View {
        VStack(spacing: .spacing6) {
            Spacer()
            
            // App Logo and Title
            VStack(spacing: .spacing4) {
                Image(systemName: "doc.text.fill")
                    .font(.system(size: 60))
                    .foregroundColor(.primary)
                
                Text("Fintech Invoice")
                    .font(.hebrewTitle)
                    .fontWeight(.bold)
                    .foregroundColor(.primary)
            }
            .padding(.bottom, .spacing8)
            
            // Login Card
            VStack(spacing: .spacing6) {
                VStack(spacing: .spacing4) {
                    Text("התחברות")
                        .font(.hebrewHeading)
                        .fontWeight(.semibold)
                        .foregroundColor(.cardForeground)
                    
                    Text("הכנס את פרטי החשבון שלך כדי להתחבר")
                        .font(.hebrewCaption)
                        .foregroundColor(.mutedForeground)
                        .multilineTextAlignment(.center)
                }
                
                VStack(spacing: .spacing4) {
                    // Email Field
                    VStack(alignment: .trailing, spacing: .spacing2) {
                        Text("כתובת אימייל")
                            .font(.hebrewCaption)
                            .foregroundColor(.cardForeground)
                        
                        TextField("<EMAIL>", text: $email)
                            .textFieldStyle(CosmicTextFieldStyle())
                            .keyboardType(.emailAddress)
                            .autocapitalization(.none)
                            .multilineTextAlignment(.trailing)
                    }
                    
                    // Password Field
                    VStack(alignment: .trailing, spacing: .spacing2) {
                        Text("סיסמה")
                            .font(.hebrewCaption)
                            .foregroundColor(.cardForeground)
                        
                        HStack {
                            Button(action: { showingPassword.toggle() }) {
                                Image(systemName: showingPassword ? "eye.slash" : "eye")
                                    .foregroundColor(.mutedForeground)
                            }
                            
                            if showingPassword {
                                TextField("••••••••", text: $password)
                                    .multilineTextAlignment(.trailing)
                            } else {
                                SecureField("••••••••", text: $password)
                                    .multilineTextAlignment(.trailing)
                            }
                        }
                        .padding(.horizontal, .spacing4)
                        .padding(.vertical, .spacing3)
                        .background(Color.inputBackground)
                        .overlay(
                            RoundedRectangle(cornerRadius: .radiusDefault)
                                .stroke(Color.border, lineWidth: 1)
                        )
                        .clipShape(RoundedRectangle(cornerRadius: .radiusDefault))
                    }
                }
                
                // Login Button
                Button(action: {
                    Task {
                        await authViewModel.signIn(email: email, password: password)
                    }
                }) {
                    HStack {
                        if authViewModel.isLoading {
                            ProgressView()
                                .scaleEffect(0.8)
                                .tint(.primaryForeground)
                        }
                        Text(authViewModel.isLoading ? "מתחבר..." : "התחבר")
                            .font(.hebrewBody)
                            .fontWeight(.medium)
                    }
                    .frame(maxWidth: .infinity)
                    .padding(.vertical, .spacing3)
                    .background(Color.primary)
                    .foregroundColor(.primaryForeground)
                    .clipShape(RoundedRectangle(cornerRadius: .radiusDefault))
                }
                .disabled(authViewModel.isLoading || email.isEmpty || password.isEmpty)
                
                // Error Message
                if let errorMessage = authViewModel.errorMessage {
                    Text(errorMessage)
                        .font(.hebrewCaption)
                        .foregroundColor(.destructive)
                        .multilineTextAlignment(.center)
                }
                
                // Register Link
                HStack {
                    Button("הירשם כאן") {
                        showingLogin = false
                    }
                    .font(.hebrewCaption)
                    .foregroundColor(.primary)
                    
                    Text("אין לך חשבון?")
                        .font(.hebrewCaption)
                        .foregroundColor(.mutedForeground)
                }
            }
            .padding(.spacing6)
            .cosmicGlass()
            .padding(.horizontal, .spacing6)
            
            Spacer()
        }
    }
}



#Preview {
    AuthenticationView()
        .environmentObject(AuthViewModel())
}
