import SwiftUI

struct RegisterView: View {
    @EnvironmentObject var authViewModel: AuthViewModel
    @Binding var showingLogin: Bool

    @State private var currentStep = 1
    @StateObject private var registrationData = RegistrationFormData()
    
    private let totalSteps = 6
    private var progress: Double {
        Double(currentStep) / Double(totalSteps)
    }
    
    var body: some View {
        VStack(spacing: .spacing6) {
            // Header with progress
            VStack(spacing: .spacing4) {
                Text("הרשמה")
                    .font(.hebrewHeading.weight(.semibold))
                    .foregroundColor(.primary)
                
                Text("צור חשבון חדש למערכת החשבוניות")
                    .font(.hebrewCaption)
                    .foregroundColor(.mutedForeground)
                    .multilineTextAlignment(.center)
                
                // Progress Bar
                ProgressView(value: progress)
                    .progressViewStyle(CosmicProgressViewStyle())
                    .padding(.horizontal, .spacing4)
            }
            .padding(.horizontal, .spacing6)
            .padding(.top, .spacing8)
            
            // Step Content
            ScrollView {
                VStack(spacing: .spacing6) {
                    Group {
                        switch currentStep {
                        case 1:
                            IndustryStep(data: Binding(
                                get: { registrationData },
                                set: { _ in }
                            ))
                        case 2:
                            BusinessDetailsStep(data: Binding(
                                get: { registrationData },
                                set: { _ in }
                            ))
                        case 3:
                            AccountDetailsStep(data: Binding(
                                get: { registrationData },
                                set: { _ in }
                            ))
                        case 4:
                            AccountingServicesStep(data: Binding(
                                get: { registrationData },
                                set: { _ in }
                            ))
                        case 5:
                            InsuranceStep(data: Binding(
                                get: { registrationData },
                                set: { _ in }
                            ))
                        case 6:
                            LoanStep(data: Binding(
                                get: { registrationData },
                                set: { _ in }
                            ))
                        default:
                            EmptyView()
                        }
                    }
                    .padding(.spacing6)
                    .cosmicGlass()
                    .padding(.horizontal, .spacing6)
                }
            }
            
            // Navigation Buttons
            HStack {
                Button("הקודם") {
                    withAnimation(.easeInOut(duration: 0.3)) {
                        currentStep = max(1, currentStep - 1)
                    }
                }
                .buttonStyle(CosmicSecondaryButtonStyle())
                .disabled(currentStep == 1)
                
                Spacer()
                
                if currentStep < totalSteps {
                    Button("הבא") {
                        if canProceedFromCurrentStep() {
                            withAnimation(.easeInOut(duration: 0.3)) {
                                currentStep = min(totalSteps, currentStep + 1)
                            }
                        } else {
                            print("⚠️ Cannot proceed - form validation failed")
                        }
                    }
                    .buttonStyle(CosmicPrimaryButtonStyle())
                    .disabled(!canProceedFromCurrentStep())
                    .opacity(canProceedFromCurrentStep() ? 1.0 : 0.6)
                    .onReceive(registrationData.$industry) { _ in
                        // Force button state update when form data changes
                    }
                    .onReceive(registrationData.$annualRevenue) { _ in
                        // Force button state update when form data changes
                    }
                } else {
                    Button(action: {
                        Task {
                            await submitRegistration()
                        }
                    }) {
                        HStack {
                            if authViewModel.isLoading {
                                ProgressView()
                                    .scaleEffect(0.8)
                                    .tint(.primaryForeground)
                            }
                            Text(authViewModel.isLoading ? "נרשם..." : "הירשם")
                        }
                    }
                    .buttonStyle(CosmicPrimaryButtonStyle())
                    .disabled(authViewModel.isLoading || !isFormValid())
                }
            }
            .padding(.horizontal, .spacing6)
            .padding(.bottom, .spacing4)
            
            // Error Message
            if let errorMessage = authViewModel.errorMessage {
                Text(errorMessage)
                    .font(.hebrewCaption)
                    .foregroundColor(.destructive)
                    .multilineTextAlignment(.center)
                    .padding(.horizontal, .spacing6)
            }
            
            // Login Link
            HStack {
                Button("התחבר כאן") {
                    showingLogin = true
                }
                .font(.hebrewCaption)
                .foregroundColor(.primary)
                
                Text("יש לך כבר חשבון?")
                    .font(.hebrewCaption)
                    .foregroundColor(.mutedForeground)
            }
            .padding(.bottom, .spacing4)
        }
        .environment(\.layoutDirection, .rightToLeft)
    }
    
    private func canProceedFromCurrentStep() -> Bool {
        switch currentStep {
        case 1:
            let canProceed = !registrationData.industry.isEmpty && !registrationData.annualRevenue.isEmpty
            print("📋 Step 1 validation - Industry: '\(registrationData.industry)', Revenue: '\(registrationData.annualRevenue)', Can proceed: \(canProceed)")
            print("📋 Registration data object ID: \(ObjectIdentifier(registrationData))")
            return canProceed
        case 2:
            let canProceed = !registrationData.companyId.isEmpty &&
                   !registrationData.nameHebrew.isEmpty &&
                   !registrationData.addressHebrew.isEmpty &&
                   !registrationData.cityHebrew.isEmpty
            print("📋 Step 2 validation - Can proceed: \(canProceed)")
            return canProceed
        case 3:
            let canProceed = !registrationData.email.isEmpty &&
                   !registrationData.password.isEmpty &&
                   !registrationData.fullName.isEmpty &&
                   !registrationData.phone.isEmpty
            print("📋 Step 3 validation - Can proceed: \(canProceed)")
            return canProceed
        default:
            return true
        }
    }
    
    private func isFormValid() -> Bool {
        return canProceedFromCurrentStep()
    }
    
    private func submitRegistration() async {
        let data = RegistrationData(
            email: registrationData.email,
            password: registrationData.password,
            fullName: registrationData.fullName,
            phone: registrationData.phone,
            companyId: registrationData.companyId,
            nameHebrew: registrationData.nameHebrew,
            nameEnglish: registrationData.nameEnglish,
            addressHebrew: registrationData.addressHebrew,
            cityHebrew: registrationData.cityHebrew,
            industry: registrationData.industry,
            annualRevenue: registrationData.annualRevenue,
            interestedInLoan: registrationData.interestedInLoan,
            interestedInInsurance: registrationData.interestedInInsurance,
            interestedInAccounting: registrationData.interestedInAccounting
        )
        
        await authViewModel.signUp(registrationData: data)
    }
}

// MARK: - Registration Form Data
class RegistrationFormData: ObservableObject {
    @Published var email = "" {
        didSet { print("📝 Email updated: \(email)") }
    }
    @Published var password = "" {
        didSet { print("📝 Password updated: [HIDDEN]") }
    }
    @Published var fullName = "" {
        didSet { print("📝 Full name updated: \(fullName)") }
    }
    @Published var phone = "" {
        didSet { print("📝 Phone updated: \(phone)") }
    }
    @Published var companyId = "" {
        didSet { print("📝 Company ID updated: \(companyId)") }
    }
    @Published var nameHebrew = "" {
        didSet { print("📝 Name Hebrew updated: \(nameHebrew)") }
    }
    @Published var nameEnglish = "" {
        didSet { print("📝 Name English updated: \(nameEnglish)") }
    }
    @Published var addressHebrew = "" {
        didSet { print("📝 Address Hebrew updated: \(addressHebrew)") }
    }
    @Published var cityHebrew = "" {
        didSet { print("📝 City Hebrew updated: \(cityHebrew)") }
    }
    @Published var industry = "" {
        didSet {
            print("📝 Industry updated: \(industry)")
        }
    }
    @Published var annualRevenue = "" {
        didSet {
            print("📝 Annual revenue updated: \(annualRevenue)")
        }
    }
    @Published var interestedInLoan = true {
        didSet { print("📝 Interested in loan updated: \(interestedInLoan)") }
    }
    @Published var interestedInInsurance = true {
        didSet { print("📝 Interested in insurance updated: \(interestedInInsurance)") }
    }
    @Published var interestedInAccounting = true {
        didSet { print("📝 Interested in accounting updated: \(interestedInAccounting)") }
    }
}

// MARK: - Custom Button Styles
struct CosmicPrimaryButtonStyle: ButtonStyle {
    func makeBody(configuration: Configuration) -> some View {
        configuration.label
            .font(.hebrewBody.weight(.medium))
            .foregroundColor(.primaryForeground)
            .padding(.horizontal, .spacing6)
            .padding(.vertical, .spacing3)
            .background(Color.primary)
            .clipShape(RoundedRectangle(cornerRadius: .radiusDefault))
            .scaleEffect(configuration.isPressed ? 0.98 : 1.0)
            .animation(.easeInOut(duration: 0.1), value: configuration.isPressed)
    }
}

struct CosmicSecondaryButtonStyle: ButtonStyle {
    func makeBody(configuration: Configuration) -> some View {
        configuration.label
            .font(.hebrewBody.weight(.medium))
            .foregroundColor(.cardForeground)
            .padding(.horizontal, .spacing6)
            .padding(.vertical, .spacing3)
            .background(Color.secondary)
            .overlay(
                RoundedRectangle(cornerRadius: .radiusDefault)
                    .stroke(Color.border, lineWidth: 1)
            )
            .clipShape(RoundedRectangle(cornerRadius: .radiusDefault))
            .scaleEffect(configuration.isPressed ? 0.98 : 1.0)
            .animation(.easeInOut(duration: 0.1), value: configuration.isPressed)
    }
}

// MARK: - Custom Progress View Style
struct CosmicProgressViewStyle: ProgressViewStyle {
    func makeBody(configuration: Configuration) -> some View {
        GeometryReader { geometry in
            ZStack(alignment: .leading) {
                RoundedRectangle(cornerRadius: .radiusSmall)
                    .fill(Color.muted)
                    .frame(height: 6)
                
                RoundedRectangle(cornerRadius: .radiusSmall)
                    .fill(Color.primary)
                    .frame(width: geometry.size.width * (configuration.fractionCompleted ?? 0), height: 6)
                    .animation(.easeInOut(duration: 0.3), value: configuration.fractionCompleted)
            }
        }
        .frame(height: 6)
    }
}

#Preview {
    RegisterView(showingLogin: .constant(false))
        .environmentObject(AuthViewModel())
}
