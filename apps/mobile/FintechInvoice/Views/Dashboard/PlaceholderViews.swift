import SwiftUI

// MARK: - Documents View
struct DocumentsView: View {
    var body: some View {
        NavigationView {
            VStack(spacing: .spacing6) {
                Image(systemName: "doc.text.fill")
                    .font(.system(size: 60))
                    .foregroundColor(.mutedForeground)
                
                Text("מסמכים")
                    .font(.hebrewHeading.weight(.semibold))
                    .foregroundColor(.cardForeground)
                
                Text("כאן תוכל לנהל את כל החשבוניות והקבלות שלך")
                    .font(.hebrewBody)
                    .foregroundColor(.mutedForeground)
                    .multilineTextAlignment(.center)
                
                Button("צור חשבונית חדשה") {
                    // TODO: Navigate to create invoice
                }
                .buttonStyle(CosmicPrimaryButtonStyle())
            }
            .padding(.spacing6)
            .frame(maxWidth: .infinity, maxHeight: .infinity)
            .background(Color.background)
            .navigationTitle("מסמכים")
            .navigationBarTitleDisplayMode(.large)
        }
        .environment(\.layoutDirection, .rightToLeft)
    }
}

// MARK: - Customers View
struct CustomersView: View {
    var body: some View {
        NavigationView {
            VStack(spacing: .spacing6) {
                Image(systemName: "person.2.fill")
                    .font(.system(size: 60))
                    .foregroundColor(.mutedForeground)
                
                Text("לקוחות")
                    .font(.hebrewHeading.weight(.semibold))
                    .foregroundColor(.cardForeground)
                
                Text("נהל את רשימת הלקוחות שלך ופרטי הקשר")
                    .font(.hebrewBody)
                    .foregroundColor(.mutedForeground)
                    .multilineTextAlignment(.center)
                
                Button("הוסף לקוח חדש") {
                    // TODO: Navigate to create customer
                }
                .buttonStyle(CosmicPrimaryButtonStyle())
            }
            .padding(.spacing6)
            .frame(maxWidth: .infinity, maxHeight: .infinity)
            .background(Color.background)
            .navigationTitle("לקוחות")
            .navigationBarTitleDisplayMode(.large)
        }
        .environment(\.layoutDirection, .rightToLeft)
    }
}

// MARK: - Expenses View
struct ExpensesView: View {
    var body: some View {
        NavigationView {
            VStack(spacing: .spacing6) {
                Image(systemName: "creditcard.fill")
                    .font(.system(size: 60))
                    .foregroundColor(.mutedForeground)
                
                Text("הוצאות")
                    .font(.hebrewHeading.weight(.semibold))
                    .foregroundColor(.cardForeground)
                
                Text("סרוק ונהל את ההוצאות העסקיות שלך")
                    .font(.hebrewBody)
                    .foregroundColor(.mutedForeground)
                    .multilineTextAlignment(.center)
                
                VStack(spacing: .spacing4) {
                    Button("סרוק קבלה") {
                        // TODO: Open camera for receipt scanning
                    }
                    .buttonStyle(CosmicPrimaryButtonStyle())
                    
                    Button("הוסף הוצאה ידנית") {
                        // TODO: Navigate to manual expense entry
                    }
                    .buttonStyle(CosmicSecondaryButtonStyle())
                }
            }
            .padding(.spacing6)
            .frame(maxWidth: .infinity, maxHeight: .infinity)
            .background(Color.background)
            .navigationTitle("הוצאות")
            .navigationBarTitleDisplayMode(.large)
        }
        .environment(\.layoutDirection, .rightToLeft)
    }
}

// MARK: - Settings View
struct SettingsView: View {
    @EnvironmentObject var authViewModel: AuthViewModel
    @State private var showingLogoutAlert = false
    
    var body: some View {
        NavigationView {
            List {
                // User Info Section
                Section {
                    if let user = authViewModel.currentUser {
                        HStack {
                            Image(systemName: "person.circle.fill")
                                .font(.title)
                                .foregroundColor(.primary)
                            
                            VStack(alignment: .leading) {
                                Text(user.email ?? "")
                                    .font(.hebrewBody)
                                    .foregroundColor(.cardForeground)
                                
                                if let company = authViewModel.selectedCompany {
                                    Text(company.nameHebrew)
                                        .font(.hebrewCaption)
                                        .foregroundColor(.mutedForeground)
                                }
                            }
                            
                            Spacer()
                        }
                        .padding(.vertical, .spacing2)
                    }
                }
                
                // App Settings Section
                Section("הגדרות אפליקציה") {
                    SettingsRow(
                        icon: "bell",
                        title: "התראות",
                        subtitle: "נהל התראות האפליקציה"
                    ) {
                        // TODO: Navigate to notifications settings
                    }
                    
                    SettingsRow(
                        icon: "moon",
                        title: "מצב כהה",
                        subtitle: "תמיד מופעל"
                    ) {
                        // TODO: Theme settings
                    }
                    
                    SettingsRow(
                        icon: "globe",
                        title: "שפה",
                        subtitle: "עברית"
                    ) {
                        // TODO: Language settings
                    }
                }
                
                // Company Settings Section
                Section("הגדרות חברה") {
                    SettingsRow(
                        icon: "building.2",
                        title: "פרטי החברה",
                        subtitle: "עדכן פרטי החברה"
                    ) {
                        // TODO: Navigate to company settings
                    }
                    
                    SettingsRow(
                        icon: "person.2",
                        title: "משתמשים",
                        subtitle: "נהל משתמשי החברה"
                    ) {
                        // TODO: Navigate to users management
                    }
                    
                    SettingsRow(
                        icon: "creditcard",
                        title: "מנוי",
                        subtitle: authViewModel.selectedCompany?.subscriptionTier == "free" ? "חינמי" : "מתקדם"
                    ) {
                        // TODO: Navigate to subscription
                    }
                }
                
                // Support Section
                Section("תמיכה") {
                    SettingsRow(
                        icon: "questionmark.circle",
                        title: "עזרה ותמיכה",
                        subtitle: "מרכז העזרה"
                    ) {
                        // TODO: Open help center
                    }
                    
                    SettingsRow(
                        icon: "envelope",
                        title: "צור קשר",
                        subtitle: "שלח הודעה לתמיכה"
                    ) {
                        // TODO: Open contact form
                    }
                    
                    SettingsRow(
                        icon: "star",
                        title: "דרג את האפליקציה",
                        subtitle: "App Store"
                    ) {
                        // TODO: Open App Store rating
                    }
                }
                
                // Account Section
                Section("חשבון") {
                    Button(action: {
                        showingLogoutAlert = true
                    }) {
                        HStack {
                            Image(systemName: "rectangle.portrait.and.arrow.right")
                                .foregroundColor(.destructive)
                            
                            Text("התנתק")
                                .font(.hebrewBody)
                                .foregroundColor(.destructive)
                            
                            Spacer()
                        }
                    }
                }
            }
            .listStyle(InsetGroupedListStyle())
            .background(Color.background)
            .navigationTitle("הגדרות")
            .navigationBarTitleDisplayMode(.large)
            .alert("התנתקות", isPresented: $showingLogoutAlert) {
                Button("בטל", role: .cancel) { }
                Button("התנתק", role: .destructive) {
                    Task {
                        await authViewModel.signOut()
                    }
                }
            } message: {
                Text("האם אתה בטוח שברצונך להתנתק?")
            }
        }
        .environment(\.layoutDirection, .rightToLeft)
    }
}

struct SettingsRow: View {
    let icon: String
    let title: String
    let subtitle: String
    let action: () -> Void
    
    var body: some View {
        Button(action: action) {
            HStack {
                Image(systemName: icon)
                    .foregroundColor(.primary)
                    .frame(width: 24)
                
                VStack(alignment: .leading, spacing: .spacing1) {
                    Text(title)
                        .font(.hebrewBody)
                        .foregroundColor(.cardForeground)
                    
                    Text(subtitle)
                        .font(.hebrewCaption)
                        .foregroundColor(.mutedForeground)
                }
                
                Spacer()
                
                Image(systemName: "chevron.right")
                    .foregroundColor(.mutedForeground)
                    .font(.caption)
            }
            .padding(.vertical, .spacing1)
        }
        .buttonStyle(PlainButtonStyle())
    }
}

#Preview("Documents") {
    DocumentsView()
}

#Preview("Customers") {
    CustomersView()
}

#Preview("Expenses") {
    ExpensesView()
}

#Preview("Settings") {
    SettingsView()
        .environmentObject(AuthViewModel())
}
