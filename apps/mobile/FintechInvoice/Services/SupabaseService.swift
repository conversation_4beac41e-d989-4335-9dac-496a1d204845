import Foundation
import Supabase
import Auth
import PostgREST

// MARK: - Data Models
struct CompanyRecord: Codable {
    let id: String?
    let businessNumber: String
    let nameHebrew: String
    let nameEnglish: String?
    let addressHebrew: String
    let cityHebrew: String
    let phone: String
    let industry: String
    let annualRevenue: String
    let interestedInLoan: Bool
    let interestedInInsurance: Bool
    let interestedInAccounting: Bool
    let subscriptionTier: String?
    let createdAt: String?
    let updatedAt: String?

    enum CodingKeys: String, CodingKey {
        case id
        case businessNumber = "business_number"
        case nameHebrew = "name_hebrew"
        case nameEnglish = "name_english"
        case addressHebrew = "address_hebrew"
        case cityHebrew = "city_hebrew"
        case phone
        case industry
        case annualRevenue = "annual_revenue"
        case interestedInLoan = "interested_in_loan"
        case interestedInInsurance = "interested_in_insurance"
        case interestedInAccounting = "interested_in_accounting"
        case subscriptionTier = "subscription_tier"
        case createdAt = "created_at"
        case updatedAt = "updated_at"
    }
}

struct UserRecord: Codable {
    let id: String
    let email: String
    let fullName: String
    let phone: String
    let createdAt: String?
    let updatedAt: String?

    enum CodingKeys: String, CodingKey {
        case id
        case email
        case fullName = "full_name"
        case phone
        case createdAt = "created_at"
        case updatedAt = "updated_at"
    }
}

struct CompanyUserRecord: Codable {
    let id: String?
    let companyId: String
    let userId: String
    let role: String
    let createdBy: String
    let createdAt: String?

    enum CodingKeys: String, CodingKey {
        case id
        case companyId = "company_id"
        case userId = "user_id"
        case role
        case createdBy = "created_by"
        case createdAt = "created_at"
    }
}

struct CompanyUserWithCompany: Codable {
    let companyId: String
    let role: String
    let companies: CompanyRecord

    enum CodingKeys: String, CodingKey {
        case companyId = "company_id"
        case role
        case companies
    }
}

class SupabaseService: ObservableObject {
    static let shared = SupabaseService()

    private var client: SupabaseClient?

    private init() {}

    private var safeClient: SupabaseClient {
        guard let client = client else {
            fatalError("SupabaseService not configured. Call configure() first.")
        }
        return client
    }

    func configure() {
        // Get Supabase credentials from environment or configuration
        guard let supabaseURL = getSupabaseURL(),
              let supabaseKey = getSupabaseAnonKey() else {
            print("❌ Supabase configuration missing. URL: \(getSupabaseURL() != nil ? "✓" : "✗"), Key: \(getSupabaseAnonKey() != nil ? "✓" : "✗")")
            fatalError("Supabase configuration missing. Please check your environment variables.")
        }

        print("🔧 Configuring Supabase with URL: \(supabaseURL)")
        client = SupabaseClient(
            supabaseURL: supabaseURL,
            supabaseKey: supabaseKey
        )
        print("✅ Supabase client configured successfully")
    }
    
    // MARK: - Authentication

    func signUp(email: String, password: String, userData: [String: AnyJSON]) async throws -> AuthResponse {
        print("🔐 Starting user signup for email: \(email)")
        let response = try await safeClient.auth.signUp(
            email: email,
            password: password,
            data: userData
        )
        print("✅ Signup successful for user: \(response.user.email ?? "unknown")")
        return response
    }

    func signIn(email: String, password: String) async throws -> Session {
        print("🔐 Starting user signin for email: \(email)")
        let session = try await safeClient.auth.signIn(
            email: email,
            password: password
        )
        print("✅ Signin successful for user: \(session.user.email ?? "unknown")")
        return session
    }

    func signOut() async throws {
        print("🔐 Starting user signout")
        try await safeClient.auth.signOut()
        print("✅ Signout successful")
    }

    func getCurrentUser() async throws -> User? {
        do {
            print("🔍 Getting current user session")
            let session = try await safeClient.auth.session
            print("✅ Current user found: \(session.user.email ?? "unknown")")
            return session.user
        } catch {
            print("⚠️ No valid session found: \(error.localizedDescription)")
            return nil
        }
    }

    func getCurrentSession() async throws -> Session {
        print("🔍 Getting current session")
        let session = try await safeClient.auth.session
        print("✅ Session retrieved for user: \(session.user.email ?? "unknown")")
        return session
    }

    // MARK: - Database Operations

    func createCompany(_ companyData: CompanyRecord) async throws -> CompanyRecord {
        print("🏢 Creating company: \(companyData.nameHebrew)")
        let response: CompanyRecord = try await safeClient.database
            .from("companies")
            .insert(companyData)
            .select()
            .single()
            .execute()
            .value
        print("✅ Company created with ID: \(response.id ?? "nil")")
        return response
    }

    func createUser(_ userData: UserRecord) async throws -> UserRecord {
        print("👤 Creating user record for: \(userData.email)")
        let response: UserRecord = try await safeClient.database
            .from("users")
            .insert(userData)
            .select()
            .single()
            .execute()
            .value
        print("✅ User record created with ID: \(response.id)")
        return response
    }

    func createCompanyUser(_ companyUserData: CompanyUserRecord) async throws -> CompanyUserRecord {
        print("🔗 Creating company-user relationship for user: \(companyUserData.userId)")
        let response: CompanyUserRecord = try await safeClient.database
            .from("company_users")
            .insert(companyUserData)
            .select()
            .single()
            .execute()
            .value
        print("✅ Company-user relationship created with ID: \(response.id ?? "nil")")
        return response
    }

    func getUserCompanies(userId: String) async throws -> [CompanyUserWithCompany] {
        print("🏢 Loading companies for user: \(userId)")
        let response: [CompanyUserWithCompany] = try await safeClient.database
            .from("company_users")
            .select("""
                company_id,
                role,
                companies (
                    id,
                    name_hebrew,
                    name_english,
                    business_number,
                    subscription_tier
                )
            """)
            .eq("user_id", value: userId)
            .execute()
            .value
        print("✅ Loaded \(response.count) companies for user")
        return response
    }
    
    // MARK: - Configuration Helpers
    
    private func getSupabaseURL() -> URL? {
        // Try to get from environment variables or configuration
        if let urlString = ProcessInfo.processInfo.environment["SUPABASE_URL"] {
            return URL(string: urlString)
        }
        
        // Fallback to configuration file or hardcoded value
        // In production, this should come from a secure configuration
        if let path = Bundle.main.path(forResource: "Config", ofType: "plist"),
           let config = NSDictionary(contentsOfFile: path),
           let urlString = config["SUPABASE_URL"] as? String {
            return URL(string: urlString)
        }
        
        return nil
    }
    
    private func getSupabaseAnonKey() -> String? {
        // Try to get from environment variables or configuration
        if let key = ProcessInfo.processInfo.environment["SUPABASE_ANON_KEY"] {
            return key
        }
        
        // Fallback to configuration file
        if let path = Bundle.main.path(forResource: "Config", ofType: "plist"),
           let config = NSDictionary(contentsOfFile: path),
           let key = config["SUPABASE_ANON_KEY"] as? String {
            return key
        }
        
        return nil
    }
}

// MARK: - Error Handling
extension SupabaseService {
    enum SupabaseError: LocalizedError {
        case configurationMissing
        case authenticationFailed
        case networkError
        case unknownError
        
        var errorDescription: String? {
            switch self {
            case .configurationMissing:
                return "Supabase configuration is missing"
            case .authenticationFailed:
                return "Authentication failed"
            case .networkError:
                return "Network error occurred"
            case .unknownError:
                return "An unknown error occurred"
            }
        }
    }
}
