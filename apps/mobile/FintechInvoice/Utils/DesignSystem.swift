import SwiftUI

// MARK: - Design System
// Matches the existing web app design system with black/white minimalist aesthetic

extension Color {
    // Core Palette - Dark Theme (Default)
    static let background = Color(hue: 0, saturation: 0, brightness: 0.08) // #141414
    static let foreground = Color(hue: 0, saturation: 0, brightness: 0.95) // #F2F2F2
    static let card = Color(hue: 0, saturation: 0, brightness: 0.12) // #1F1F1F
    static let cardForeground = Color(hue: 0, saturation: 0, brightness: 0.95) // #F2F2F2
    static let primary = Color(hue: 0, saturation: 0, brightness: 0.85) // #D9D9D9
    static let primaryForeground = Color(hue: 0, saturation: 0, brightness: 0.15) // #262626
    static let secondary = Color(hue: 0, saturation: 0, brightness: 0.18) // #2E2E2E
    static let secondaryForeground = Color(hue: 0, saturation: 0, brightness: 0.95) // #F2F2F2
    static let muted = Color(hue: 0, saturation: 0, brightness: 0.22) // #383838
    static let mutedForeground = Color(hue: 0, saturation: 0, brightness: 0.70) // #B3B3B3
    static let accentForeground = Color(hue: 0, saturation: 0, brightness: 0.15) // #262626
    static let border = Color(hue: 0, saturation: 0, brightness: 0.22) // #383838
    static let inputBackground = Color(hue: 0, saturation: 0, brightness: 0.22) // #383838
    static let ring = Color(hue: 0, saturation: 0, brightness: 0.70) // #B3B3B3
    
    // Cosmic Accent Colors
    static let cosmicDark = Color(red: 0.25, green: 0.25, blue: 0.25) // #404040
    static let cosmicDarker = Color(red: 0.19, green: 0.19, blue: 0.19) // #303030
    static let cosmicLight = Color(red: 0.94, green: 0.94, blue: 0.94) // #f0f0f0
    static let cosmicAccent = Color(red: 0.38, green: 0.38, blue: 0.38) // #606060
    static let cosmicMuted = Color(red: 0.56, green: 0.56, blue: 0.56) // #909090
    
    // State Colors
    static let destructive = Color(red: 0.96, green: 0.40, blue: 0.40) // #F56565
    static let destructiveForeground = Color(red: 0.97, green: 0.98, blue: 0.99) // #F7FAFC
    
    // Success and Warning (for future use)
    static let success = Color(red: 0.30, green: 0.70, blue: 0.30)
    static let warning = Color(red: 0.90, green: 0.70, blue: 0.20)
}

// MARK: - Typography
extension Font {
    // Hebrew Font - SecularOne equivalent
    static let hebrewTitle = Font.custom("SecularOne-Regular", size: 24)
    static let hebrewHeading = Font.custom("SecularOne-Regular", size: 20)
    static let hebrewBody = Font.custom("SecularOne-Regular", size: 16)
    static let hebrewCaption = Font.custom("SecularOne-Regular", size: 14)
    
    // English Font - Inter equivalent
    static let englishTitle = Font.custom("Inter-SemiBold", size: 24)
    static let englishHeading = Font.custom("Inter-Medium", size: 20)
    static let englishBody = Font.custom("Inter-Regular", size: 16)
    static let englishCaption = Font.custom("Inter-Regular", size: 14)
    
    // Fallback system fonts
    static let titleFallback = Font.title2.weight(.semibold)
    static let headingFallback = Font.headline.weight(.medium)
    static let bodyFallback = Font.body
    static let captionFallback = Font.caption
}

// MARK: - Spacing
extension CGFloat {
    // Base spacing scale (matches Tailwind)
    static let spacing1: CGFloat = 4    // 0.25rem
    static let spacing2: CGFloat = 8    // 0.5rem
    static let spacing3: CGFloat = 12   // 0.75rem
    static let spacing4: CGFloat = 16   // 1rem
    static let spacing5: CGFloat = 20   // 1.25rem
    static let spacing6: CGFloat = 24   // 1.5rem
    static let spacing8: CGFloat = 32   // 2rem
    static let spacing10: CGFloat = 40  // 2.5rem
    static let spacing12: CGFloat = 48  // 3rem
    static let spacing16: CGFloat = 64  // 4rem
    static let spacing20: CGFloat = 80  // 5rem
    static let spacing24: CGFloat = 96  // 6rem
    
    // Component specific spacing
    static let cardPadding: CGFloat = 24
    static let buttonPadding: CGFloat = 16
    static let sectionSpacing: CGFloat = 80
    static let containerPadding: CGFloat = 24
}

// MARK: - Border Radius
extension CGFloat {
    static let radiusSmall: CGFloat = 6    // rounded-md
    static let radiusDefault: CGFloat = 8  // rounded-lg
    static let radiusLarge: CGFloat = 12   // rounded-xl
    static let radiusXLarge: CGFloat = 16  // rounded-2xl
    static let radiusFull: CGFloat = 9999  // rounded-full
}

// MARK: - Shadows
extension View {
    func cosmicGlow() -> some View {
        self.shadow(color: Color.white.opacity(0.2), radius: 10, x: 0, y: 0)
    }
    
    func cardShadow() -> some View {
        self.shadow(color: Color.black.opacity(0.1), radius: 3, x: 0, y: 1)
    }
    
    func pricingGlow() -> some View {
        self.shadow(color: Color.primary.opacity(0.15), radius: 10, x: 0, y: 0)
    }
}

// MARK: - Custom Modifiers
struct CosmicCard: ViewModifier {
    func body(content: Content) -> some View {
        content
            .background(Color.card)
            .overlay(
                RoundedRectangle(cornerRadius: .radiusDefault)
                    .stroke(Color.border, lineWidth: 1)
            )
            .clipShape(RoundedRectangle(cornerRadius: .radiusDefault))
    }
}

struct CosmicGlass: ViewModifier {
    func body(content: Content) -> some View {
        content
            .background(.ultraThinMaterial)
            .overlay(
                RoundedRectangle(cornerRadius: .radiusDefault)
                    .stroke(Color.white.opacity(0.1), lineWidth: 1)
            )
            .clipShape(RoundedRectangle(cornerRadius: .radiusDefault))
            .shadow(color: Color.white.opacity(0.1), radius: 8, x: 0, y: 0)
    }
}

extension View {
    func cosmicCard() -> some View {
        modifier(CosmicCard())
    }

    func cosmicGlass() -> some View {
        modifier(CosmicGlass())
    }
}

// MARK: - Text Field Style
struct CosmicTextFieldStyle: TextFieldStyle {
    func _body(configuration: TextField<Self._Label>) -> some View {
        configuration
            .padding(.horizontal, .spacing4)
            .padding(.vertical, .spacing3)
            .background(Color.inputBackground)
            .overlay(
                RoundedRectangle(cornerRadius: .radiusDefault)
                    .stroke(Color.border, lineWidth: 1)
            )
            .clipShape(RoundedRectangle(cornerRadius: .radiusDefault))
    }
}
