import Foundation
import SwiftUI
import Supabase

@MainActor
class AuthViewModel: ObservableObject {
    @Published var isAuthenticated = false
    @Published var currentUser: User?
    @Published var userCompanies: [Company] = []
    @Published var selectedCompany: Company?
    @Published var isLoading = false
    @Published var errorMessage: String?
    
    private let supabaseService = SupabaseService.shared
    
    init() {
        // Listen for auth state changes
        Task {
            await checkAuthStatus()
        }
    }
    
    func checkAuthStatus() async {
        print("🔍 Checking authentication status...")
        do {
            let user = try await supabaseService.getCurrentUser()
            currentUser = user
            isAuthenticated = user != nil

            if let user = user {
                print("✅ User authenticated: \(user.email ?? "unknown")")
                await loadUserCompanies()
            } else {
                print("ℹ️ No authenticated user found")
                userCompanies = []
                selectedCompany = nil
            }
        } catch {
            print("❌ Auth check error: \(error.localizedDescription)")
            isAuthenticated = false
            currentUser = nil
            userCompanies = []
            selectedCompany = nil
        }
    }
    
    func signIn(email: String, password: String) async {
        print("🔐 Starting signin process for: \(email)")
        isLoading = true
        errorMessage = nil

        do {
            let session = try await supabaseService.signIn(email: email, password: password)
            currentUser = session.user
            isAuthenticated = true
            print("✅ Signin successful, loading user companies...")
            await loadUserCompanies()
        } catch {
            let errorMsg: String
            if error.localizedDescription.contains("Invalid login credentials") {
                errorMsg = "שגיאה: אימייל או סיסמה שגויים"
                print("❌ Sign in error: Invalid credentials for \(email)")
            } else {
                errorMsg = "שגיאה בהתחברות: \(error.localizedDescription)"
                print("❌ Sign in error: \(error.localizedDescription)")
            }
            errorMessage = errorMsg
        }

        isLoading = false
        print("🔐 Signin process completed")
    }
    
    func signUp(registrationData: RegistrationData) async {
        print("📝 Starting signup process for: \(registrationData.email)")
        isLoading = true
        errorMessage = nil

        do {
            // Prepare user metadata
            let userData: [String: AnyJSON] = [
                "full_name": AnyJSON.string(registrationData.fullName),
                "phone": AnyJSON.string(registrationData.phone)
            ]
            print("📝 Prepared user metadata")

            // Sign up user
            let authResponse = try await supabaseService.signUp(
                email: registrationData.email,
                password: registrationData.password,
                userData: userData
            )

            let user = authResponse.user
            print("✅ User signup successful: \(user.email ?? "unknown")")

            // Create user record
            let userRecord = UserRecord(
                id: user.id.uuidString,
                email: registrationData.email,
                fullName: registrationData.fullName,
                phone: registrationData.phone,
                createdAt: nil,
                updatedAt: nil
            )

            _ = try await supabaseService.createUser(userRecord)

            // Create company record
            let companyRecord = CompanyRecord(
                id: nil,
                businessNumber: registrationData.companyId,
                nameHebrew: registrationData.nameHebrew,
                nameEnglish: registrationData.nameEnglish,
                addressHebrew: registrationData.addressHebrew,
                cityHebrew: registrationData.cityHebrew,
                phone: registrationData.phone,
                industry: registrationData.industry,
                annualRevenue: registrationData.annualRevenue,
                interestedInLoan: registrationData.interestedInLoan,
                interestedInInsurance: registrationData.interestedInInsurance,
                interestedInAccounting: registrationData.interestedInAccounting,
                subscriptionTier: "free",
                createdAt: nil,
                updatedAt: nil
            )

            let company = try await supabaseService.createCompany(companyRecord)

            // Create company-user relationship
            guard let companyId = company.id else {
                print("❌ Error: Company ID is nil after creation")
                throw SupabaseService.SupabaseError.authenticationFailed
            }

            let companyUserRecord = CompanyUserRecord(
                id: nil,
                companyId: companyId,
                userId: user.id.uuidString,
                role: "admin",
                createdBy: user.id.uuidString,
                createdAt: nil
            )

            _ = try await supabaseService.createCompanyUser(companyUserRecord)

            currentUser = user
            isAuthenticated = true
            await loadUserCompanies()

        } catch {
            let errorMsg = "שגיאה ברישום: \(error.localizedDescription)"
            errorMessage = errorMsg
            print("❌ Sign up error: \(error.localizedDescription)")
        }

        isLoading = false
        print("📝 Signup process completed")
    }
    
    func signOut() async {
        isLoading = true
        
        do {
            try await supabaseService.signOut()
            isAuthenticated = false
            currentUser = nil
            userCompanies = []
            selectedCompany = nil
        } catch {
            errorMessage = "שגיאה ביציאה: \(error.localizedDescription)"
            print("Sign out error: \(error)")
        }
        
        isLoading = false
    }
    
    private func loadUserCompanies() async {
        guard let user = currentUser else {
            print("⚠️ Cannot load companies: no current user")
            return
        }

        print("🏢 Loading companies for user: \(user.id.uuidString)")
        do {
            let companiesData = try await supabaseService.getUserCompanies(userId: user.id.uuidString)
            print("📊 Received \(companiesData.count) company records")

            userCompanies = companiesData.compactMap { data in
                let companyData = data.companies
                guard let companyId = companyData.id else {
                    print("⚠️ Warning: Skipping company with nil ID")
                    return nil
                }
                return Company(
                    id: companyId,
                    nameHebrew: companyData.nameHebrew,
                    nameEnglish: companyData.nameEnglish,
                    businessNumber: companyData.businessNumber,
                    subscriptionTier: companyData.subscriptionTier ?? "free"
                )
            }

            print("✅ Processed \(userCompanies.count) valid companies")

            // Select first company by default
            if selectedCompany == nil && !userCompanies.isEmpty {
                selectedCompany = userCompanies.first
                print("🎯 Auto-selected first company: \(selectedCompany?.nameHebrew ?? "unknown")")
            }

        } catch {
            print("❌ Error loading companies: \(error.localizedDescription)")
        }
    }
}

// MARK: - Data Models
struct RegistrationData {
    let email: String
    let password: String
    let fullName: String
    let phone: String
    let companyId: String
    let nameHebrew: String
    let nameEnglish: String?
    let addressHebrew: String
    let cityHebrew: String
    let industry: String
    let annualRevenue: String
    let interestedInLoan: Bool
    let interestedInInsurance: Bool
    let interestedInAccounting: Bool
}

struct Company: Identifiable, Codable {
    let id: String
    let nameHebrew: String
    let nameEnglish: String?
    let businessNumber: String
    let subscriptionTier: String
}
