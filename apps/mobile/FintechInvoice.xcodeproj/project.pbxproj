// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 56;
	objects = {

/* Begin PBXBuildFile section */
		FA001001 /* FintechInvoiceApp.swift in Sources */ = {isa = PBXBuildFile; fileRef = FA001000 /* FintechInvoiceApp.swift */; };
		FA001003 /* ContentView.swift in Sources */ = {isa = PBXBuildFile; fileRef = FA001002 /* ContentView.swift */; };
		FA001005 /* Assets.xcassets in Resources */ = {isa = PBXBuildFile; fileRef = FA001004 /* Assets.xcassets */; };
		FA001008 /* Preview Assets.xcassets in Resources */ = {isa = PBXBuildFile; fileRef = FA001007 /* Preview Assets.xcassets */; };
		FA001010 /* DesignSystem.swift in Sources */ = {isa = PBXBuildFile; fileRef = FA001009 /* DesignSystem.swift */; };
		FA001012 /* SupabaseService.swift in Sources */ = {isa = PBXBuildFile; fileRef = FA001011 /* SupabaseService.swift */; };
		FA001014 /* AuthViewModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = FA001013 /* AuthViewModel.swift */; };
		FA001016 /* AuthenticationView.swift in Sources */ = {isa = PBXBuildFile; fileRef = FA001015 /* AuthenticationView.swift */; };
		FA001018 /* RegisterView.swift in Sources */ = {isa = PBXBuildFile; fileRef = FA001017 /* RegisterView.swift */; };
		FA001020 /* RegistrationSteps.swift in Sources */ = {isa = PBXBuildFile; fileRef = FA001019 /* RegistrationSteps.swift */; };
		FA001022 /* MainTabView.swift in Sources */ = {isa = PBXBuildFile; fileRef = FA001021 /* MainTabView.swift */; };
		FA001024 /* DashboardViewModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = FA001023 /* DashboardViewModel.swift */; };
		FA001026 /* PlaceholderViews.swift in Sources */ = {isa = PBXBuildFile; fileRef = FA001025 /* PlaceholderViews.swift */; };
		FA001028 /* Config.plist in Resources */ = {isa = PBXBuildFile; fileRef = FA001027 /* Config.plist */; };
		FA001050 /* Supabase in Frameworks */ = {isa = PBXBuildFile; productRef = FA001051 /* Supabase */; };
/* End PBXBuildFile section */

/* Begin PBXFileReference section */
		FA000FFD /* FintechInvoice.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = FintechInvoice.app; sourceTree = BUILT_PRODUCTS_DIR; };
		FA001000 /* FintechInvoiceApp.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = FintechInvoiceApp.swift; sourceTree = "<group>"; };
		FA001002 /* ContentView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ContentView.swift; sourceTree = "<group>"; };
		FA001004 /* Assets.xcassets */ = {isa = PBXFileReference; lastKnownFileType = folder.assetcatalog; path = Assets.xcassets; sourceTree = "<group>"; };
		FA001007 /* Preview Assets.xcassets */ = {isa = PBXFileReference; lastKnownFileType = folder.assetcatalog; path = "Preview Assets.xcassets"; sourceTree = "<group>"; };
		FA001009 /* DesignSystem.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = DesignSystem.swift; sourceTree = "<group>"; };
		FA001011 /* SupabaseService.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = SupabaseService.swift; sourceTree = "<group>"; };
		FA001013 /* AuthViewModel.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = AuthViewModel.swift; sourceTree = "<group>"; };
		FA001015 /* AuthenticationView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = AuthenticationView.swift; sourceTree = "<group>"; };
		FA001017 /* RegisterView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = RegisterView.swift; sourceTree = "<group>"; };
		FA001019 /* RegistrationSteps.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = RegistrationSteps.swift; sourceTree = "<group>"; };
		FA001021 /* MainTabView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = MainTabView.swift; sourceTree = "<group>"; };
		FA001023 /* DashboardViewModel.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = DashboardViewModel.swift; sourceTree = "<group>"; };
		FA001025 /* PlaceholderViews.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = PlaceholderViews.swift; sourceTree = "<group>"; };
		FA001027 /* Config.plist */ = {isa = PBXFileReference; lastKnownFileType = text.plist.xml; path = Config.plist; sourceTree = "<group>"; };
		FA001029 /* Info.plist */ = {isa = PBXFileReference; lastKnownFileType = text.plist.xml; path = Info.plist; sourceTree = "<group>"; };
/* End PBXFileReference section */

/* Begin PBXFrameworksBuildPhase section */
		FA000FFA /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				FA001050 /* Supabase in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		FA000FF4 = {
			isa = PBXGroup;
			children = (
				FA000FFF /* FintechInvoice */,
				FA000FFE /* Products */,
			);
			sourceTree = "<group>";
		};
		FA000FFE /* Products */ = {
			isa = PBXGroup;
			children = (
				FA000FFD /* FintechInvoice.app */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		FA000FFF /* FintechInvoice */ = {
			isa = PBXGroup;
			children = (
				FA001000 /* FintechInvoiceApp.swift */,
				FA001002 /* ContentView.swift */,
				FA001040 /* Views */,
				FA001041 /* ViewModels */,
				FA001042 /* Services */,
				FA001043 /* Utils */,
				FA001004 /* Assets.xcassets */,
				FA001027 /* Config.plist */,
				FA001029 /* Info.plist */,
				FA001006 /* Preview Content */,
			);
			path = FintechInvoice;
			sourceTree = "<group>";
		};
		FA001006 /* Preview Content */ = {
			isa = PBXGroup;
			children = (
				FA001007 /* Preview Assets.xcassets */,
			);
			path = "Preview Content";
			sourceTree = "<group>";
		};
		FA001040 /* Views */ = {
			isa = PBXGroup;
			children = (
				FA001044 /* Auth */,
				FA001045 /* Dashboard */,
			);
			path = Views;
			sourceTree = "<group>";
		};
		FA001041 /* ViewModels */ = {
			isa = PBXGroup;
			children = (
				FA001013 /* AuthViewModel.swift */,
				FA001023 /* DashboardViewModel.swift */,
			);
			path = ViewModels;
			sourceTree = "<group>";
		};
		FA001042 /* Services */ = {
			isa = PBXGroup;
			children = (
				FA001011 /* SupabaseService.swift */,
			);
			path = Services;
			sourceTree = "<group>";
		};
		FA001043 /* Utils */ = {
			isa = PBXGroup;
			children = (
				FA001009 /* DesignSystem.swift */,
			);
			path = Utils;
			sourceTree = "<group>";
		};
		FA001044 /* Auth */ = {
			isa = PBXGroup;
			children = (
				FA001015 /* AuthenticationView.swift */,
				FA001017 /* RegisterView.swift */,
				FA001019 /* RegistrationSteps.swift */,
			);
			path = Auth;
			sourceTree = "<group>";
		};
		FA001045 /* Dashboard */ = {
			isa = PBXGroup;
			children = (
				FA001021 /* MainTabView.swift */,
				FA001025 /* PlaceholderViews.swift */,
			);
			path = Dashboard;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		FA000FFC /* FintechInvoice */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = FA00100B /* Build configuration list for PBXNativeTarget "FintechInvoice" */;
			buildPhases = (
				FA000FF9 /* Sources */,
				FA000FFA /* Frameworks */,
				FA000FFB /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = FintechInvoice;
			packageProductDependencies = (
				FA001051 /* Supabase */,
			);
			productName = FintechInvoice;
			productReference = FA000FFD /* FintechInvoice.app */;
			productType = "com.apple.product-type.application";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		FA000FF5 /* Project object */ = {
			isa = PBXProject;
			attributes = {
				BuildIndependentTargetsInParallel = 1;
				LastSwiftUpdateCheck = 1500;
				LastUpgradeCheck = 1500;
				TargetAttributes = {
					FA000FFC = {
						CreatedOnToolsVersion = 15.0;
					};
				};
			};
			buildConfigurationList = FA000FF8 /* Build configuration list for PBXProject "FintechInvoice" */;
			compatibilityVersion = "Xcode 14.0";
			developmentRegion = he;
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
				he,
			);
			mainGroup = FA000FF4;
			packageReferences = (
				CE1651F22E62E5AE00E8504C /* XCRemoteSwiftPackageReference "supabase-swift" */,
			);
			productRefGroup = FA000FFE /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				FA000FFC /* FintechInvoice */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		FA000FFB /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				FA001008 /* Preview Assets.xcassets in Resources */,
				FA001005 /* Assets.xcassets in Resources */,
				FA001028 /* Config.plist in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		FA000FF9 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				FA001003 /* ContentView.swift in Sources */,
				FA001001 /* FintechInvoiceApp.swift in Sources */,
				FA001010 /* DesignSystem.swift in Sources */,
				FA001012 /* SupabaseService.swift in Sources */,
				FA001014 /* AuthViewModel.swift in Sources */,
				FA001016 /* AuthenticationView.swift in Sources */,
				FA001018 /* RegisterView.swift in Sources */,
				FA001020 /* RegistrationSteps.swift in Sources */,
				FA001022 /* MainTabView.swift in Sources */,
				FA001024 /* DashboardViewModel.swift in Sources */,
				FA001026 /* PlaceholderViews.swift in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin XCBuildConfiguration section */
		FA000FF6 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				CL_WARN_STRICT_PROTOTYPES = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = dwarf;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 15.0;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MTL_ENABLE_DEBUG_INFO = INCLUDE_SOURCE;
				MTL_FAST_MATH = YES;
				ONLY_ACTIVE_ARCH = YES;
				SDKROOT = iphoneos;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "DEBUG $(inherited)";
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
			};
			name = Debug;
		};
		FA000FF7 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 15.0;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MTL_ENABLE_DEBUG_INFO = NO;
				MTL_FAST_MATH = YES;
				SDKROOT = iphoneos;
				SWIFT_COMPILATION_MODE = wholemodule;
				VALIDATE_PRODUCT = YES;
			};
			name = Release;
		};
		FA001030 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_ASSET_PATHS = "\"FintechInvoice/Preview Content\"";
				DEVELOPMENT_TEAM = "";
				ENABLE_PREVIEWS = YES;
				GENERATE_INFOPLIST_FILE = NO;
				INFOPLIST_FILE = FintechInvoice/Info.plist;
				INFOPLIST_KEY_UIApplicationSceneManifest_Generation = YES;
				INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents = YES;
				INFOPLIST_KEY_UILaunchScreen_Generation = YES;
				INFOPLIST_KEY_UISupportedInterfaceOrientations = UIInterfaceOrientationPortrait;
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad = "UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown";
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.fintech.invoice.app;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator";
				SUPPORTS_MACCATALYST = NO;
				SUPPORTS_MAC_DESIGNED_FOR_IPHONE_IPAD = NO;
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = 1;
			};
			name = Debug;
		};
		FA001031 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_ASSET_PATHS = "\"FintechInvoice/Preview Content\"";
				DEVELOPMENT_TEAM = "";
				ENABLE_PREVIEWS = YES;
				GENERATE_INFOPLIST_FILE = NO;
				INFOPLIST_FILE = FintechInvoice/Info.plist;
				INFOPLIST_KEY_UIApplicationSceneManifest_Generation = YES;
				INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents = YES;
				INFOPLIST_KEY_UILaunchScreen_Generation = YES;
				INFOPLIST_KEY_UISupportedInterfaceOrientations = UIInterfaceOrientationPortrait;
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad = "UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown";
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.fintech.invoice.app;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator";
				SUPPORTS_MACCATALYST = NO;
				SUPPORTS_MAC_DESIGNED_FOR_IPHONE_IPAD = NO;
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = 1;
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		FA000FF8 /* Build configuration list for PBXProject "FintechInvoice" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				FA000FF6 /* Debug */,
				FA000FF7 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		FA00100B /* Build configuration list for PBXNativeTarget "FintechInvoice" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				FA001030 /* Debug */,
				FA001031 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */

/* Begin XCRemoteSwiftPackageReference section */
		CE1651F22E62E5AE00E8504C /* XCRemoteSwiftPackageReference "supabase-swift" */ = {
			isa = XCRemoteSwiftPackageReference;
			repositoryURL = "https://github.com/supabase/supabase-swift.git";
			requirement = {
				kind = upToNextMajorVersion;
				minimumVersion = 2.5.1;
			};
		};
		FA001052 /* XCRemoteSwiftPackageReference "supabase-swift" */ = {
			isa = XCRemoteSwiftPackageReference;
			repositoryURL = "https://github.com/supabase/supabase-swift.git";
			requirement = {
				kind = upToNextMajorVersion;
				minimumVersion = 2.5.1;
			};
		};
/* End XCRemoteSwiftPackageReference section */

/* Begin XCSwiftPackageProductDependency section */
		FA001051 /* Supabase */ = {
			isa = XCSwiftPackageProductDependency;
			package = FA001052 /* XCRemoteSwiftPackageReference "supabase-swift" */;
			productName = Supabase;
		};
/* End XCSwiftPackageProductDependency section */
	};
	rootObject = FA000FF5 /* Project object */;
}
